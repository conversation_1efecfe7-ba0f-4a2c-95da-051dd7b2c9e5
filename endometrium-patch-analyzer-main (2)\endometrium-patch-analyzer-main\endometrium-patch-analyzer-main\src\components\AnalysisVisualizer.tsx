import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BarChart3Icon, PieChartIcon, TableIcon } from 'lucide-react';
import { toast } from 'sonner';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, ResponsiveContainer, Cell, PieChart, Pie, LineChart, Line } from 'recharts';
import { motion } from 'framer-motion';
import { analyzeImage, loadModel, calculateClinicalMatrices, getTSRInterpretation, getTVRInterpretation, getSVRInterpretation } from '@/lib/modelService';
import * as tf from '@tensorflow/tfjs';

interface AnalysisResult {
  tumor: number;
  stroma: number;
  vessel: number;
  patchId: string;
  patchNumber?: number;
}

interface ClinicalMatrices {
  tsr: number;
  tvr: number;
  svr: number;
  tsrStatus: 'Good' | 'Poor';
  tvrStatus: 'Good' | 'Poor';
  svrStatus: 'Good' | 'Poor';
  overallPrognosis: string;
  confidenceScore: number;
  prognosticFactors: Array<{ impact: 'positive' | 'negative'; description: string }>;
  recommendations: string[];
}

interface ExtremeValue {
  index: number;
  value: number;
  patchId: string;
  patchNumber?: number;
}

interface ExtremeValues {
  tumor: { highest: ExtremeValue | null; lowest: ExtremeValue | null };
  stroma: { highest: ExtremeValue | null; lowest: ExtremeValue | null };
  vessel: { highest: ExtremeValue | null; lowest: ExtremeValue | null };
}

interface AnalysisVisualizerProps {
  files: File[];
  results: AnalysisResult[];
  selectedIndex: number;
  clinicalMatrices?: any;
  onAnalyze: (files: File[]) => void;
  onAnalysisComplete: (results: AnalysisResult[]) => void;
}

const AnalysisVisualizer: React.FC<AnalysisVisualizerProps> = ({
  files = [],
  results: initialResults = [],
  selectedIndex = 0,
  clinicalMatrices: initialClinicalMatrices,
  onAnalyze,
  onAnalysisComplete
}) => {
  const [analyzing, setAnalyzing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [analysisComplete, setAnalysisComplete] = useState(false);
  const [modelLoaded, setModelLoaded] = useState(false);
  const [results, setResults] = useState<AnalysisResult[]>(initialResults || []);
  const [clinicalMatrices, setClinicalMatrices] = useState(initialClinicalMatrices || {});

  // Reset when files change
  useEffect(() => {
    setAnalysisComplete(false);
    setProgress(0);
    setResults([]);
    setClinicalMatrices({});
  }, [files]);

  // Load model on component mount
  useEffect(() => {
    const initModel = async () => {
      try {
        // First, make sure TensorFlow.js is loaded
        await tf.ready();
        console.log("TensorFlow.js is ready");

        // Load the model
        console.log("Loading TensorFlow.js model...");
        const loaded = await loadModel();
        console.log("Model loading result:", loaded);

        setModelLoaded(loaded);

        if (loaded) {
          console.log("TensorFlow.js model is active");
          toast.success("TensorFlow.js model loaded successfully");
        } else {
          console.error("Failed to load TensorFlow.js model");
          toast.error("Failed to load TensorFlow.js model. Please reload the page.");
        }
      } catch (error) {
        console.error("Error loading model:", error);
        toast.error("Failed to load analysis model");
        setModelLoaded(false);
      }
    };

    // Call the initialization function
    initModel();
  }, []);

  const runAnalysis = async () => {
    if (files.length === 0) {
      toast.error("No files to analyze");
      return;
    }

    // Check if TensorFlow.js model is loaded
    if (!modelLoaded) {
      toast.error("TensorFlow.js model is not loaded yet. Please wait.");
      return;
    }

    setAnalyzing(true);
    setProgress(0);

    // Process files using the model
    let processedCount = 0;
    const totalFiles = files.length;
    const analysisResults: AnalysisResult[] = [];
    let analysisErrors = 0;

    const processNextFile = async () => {
      if (processedCount >= totalFiles) {
        // Analysis complete
        setAnalysisComplete(true);
        setAnalyzing(false);

        if (analysisResults.length === 0) {
          // If all files failed, show an error
          toast.error("Analysis failed for all files. Please check the console for details.");
        } else {
          // Calculate clinical matrices
          const matrices = calculateClinicalMatrices(analysisResults);
          setClinicalMatrices(matrices);

          onAnalysisComplete(analysisResults);
          setResults(analysisResults);

          if (analysisErrors > 0) {
            toast.warning(`Analysis complete with ${analysisErrors} errors. ${analysisResults.length} files analyzed successfully.`);
          } else {
            toast.success(`Analysis complete for ${totalFiles} patches`);
          }
        }
        return;
      }

      try {
        // Analyze the current file using our model service
        console.log(`Analyzing file ${processedCount + 1}/${totalFiles}: ${files[processedCount].name}`);
        const result = await analyzeImage(files[processedCount]);
        analysisResults.push(result);

        processedCount++;

        // Update progress
        const newProgress = Math.round((processedCount / totalFiles) * 100);
        setProgress(newProgress);

        // Process next file
        setTimeout(() => processNextFile(), 300);
      } catch (error) {
        console.error("Error analyzing file:", error);
        toast.error(`Error analyzing ${files[processedCount].name}`);

        // Count the error
        analysisErrors++;

        // Continue with next file
        processedCount++;
        setTimeout(() => processNextFile(), 300);
      }
    };

    // Start processing
    processNextFile();
  };

  // Get current result with fallback
  const currentResult = results[selectedIndex] || {
    patchId: '',
    stroma: 0,
    tumor: 0,
    vessel: 0
  };

  // Log current result for debugging
  useEffect(() => {
    console.log('Current result:', currentResult);
  }, [currentResult]);

  const calculateExtremeValues = useCallback(() => {
    if (!results || results.length === 0) {
      return {
        tumor: { highest: null, lowest: null },
        stroma: { highest: null, lowest: null },
        vessel: { highest: null, lowest: null }
      };
    }

    const extremes: ExtremeValues = {
      tumor: { highest: null, lowest: null },
      stroma: { highest: null, lowest: null },
      vessel: { highest: null, lowest: null }
    };

    results.forEach((result, index) => {
      if (!result) return;

      ['tumor', 'stroma', 'vessel'].forEach((type) => {
        const value = result[type as keyof AnalysisResult];
        if (typeof value !== 'number') return;

        if (!extremes[type as keyof ExtremeValues].highest || value > extremes[type as keyof ExtremeValues].highest!.value) {
          extremes[type as keyof ExtremeValues].highest = {
            index,
            value,
            patchId: result.patchId || `patch_${index}`,
            patchNumber: result.patchNumber
          };
        }
        if (!extremes[type as keyof ExtremeValues].lowest || value < extremes[type as keyof ExtremeValues].lowest!.value) {
          extremes[type as keyof ExtremeValues].lowest = {
            index,
            value,
            patchId: result.patchId || `patch_${index}`,
            patchNumber: result.patchNumber
          };
        }
      });
    });

    return extremes;
  }, [results]);

  const extremeValues = useMemo(() => calculateExtremeValues(), [calculateExtremeValues]);

  const renderExtremeValueCard = (type: keyof ExtremeValues, extreme: 'highest' | 'lowest') => {
    const data = extremeValues[type]?.[extreme];
    if (!data) return null;

    const colorClass = {
      tumor: 'text-red-600',
      stroma: 'text-blue-600',
      vessel: 'text-green-600'
    }[type];

    return (
      <div className="bg-white rounded-lg shadow-md p-4 m-2">
        <h3 className={`text-lg font-semibold capitalize ${colorClass}`}>
          {extreme} {type} Percentage
        </h3>
        <p className="text-gray-600">
          Patch {data.patchNumber || data.index + 1}: {data.value.toFixed(2)}%
        </p>
      </div>
    );
  };

  const getAverageResults = () => {
    if (results.length === 0) return { stroma: 0, tumor: 0, vessel: 0 };

    const avgStroma = results.reduce((sum, r) => sum + r.stroma, 0) / results.length;
    const avgTumor = results.reduce((sum, r) => sum + r.tumor, 0) / results.length;
    const avgVessel = results.reduce((sum, r) => sum + r.vessel, 0) / results.length;

    return {
      stroma: Math.round(avgStroma),
      tumor: Math.round(avgTumor),
      vessel: Math.round(avgVessel)
    };
  };

  const averages = getAverageResults();

  // Chart data with null checks
  const pieData = currentResult ? [
    { name: 'Stroma', value: currentResult.stroma, color: '#4DA6FF' },
    { name: 'Tumor', value: currentResult.tumor, color: '#FF5252' },
    { name: 'Blood Vessel', value: currentResult.vessel, color: '#4CAF50' }
  ] : [];

  const barData = results.map((r, i) => ({
    name: `Patch ${i+1}`,
    stroma: r?.stroma || 0,
    tumor: r?.tumor || 0,
    vessel: r?.vessel || 0
  }));

  return (
    <div className="container mx-auto p-4">
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <span>Tissue Analysis</span>
              {modelLoaded && (
                <Badge variant="default">
                  TensorFlow.js Model
                </Badge>
              )}
            </div>
            {!analyzing && !analysisComplete && (
              <Button onClick={runAnalysis}>
                Analyze Patches
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {analyzing && (
            <div className="space-y-2">
              <div className="text-center text-sm font-medium">
                Analyzing patches... {progress}%
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}

          {analysisComplete && (
            <div className="space-y-6">
              {/* Clean Tissue Analysis Results */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-xl shadow-lg border border-gray-100 p-8"
              >
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-primary-navy mb-2">Tissue Analysis Results</h3>
                  <p className="text-gray-600">AI-powered tissue composition analysis</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {/* Stroma Content */}
                  <div className="text-center">
                    <div className="bg-blue-50 rounded-full w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-600">
                          {currentResult?.stroma || 0}%
                        </div>
                      </div>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-800 mb-2">Stroma Content</h4>
                    <p className="text-sm text-gray-500">Connective tissue percentage</p>
                    <div className="mt-3 bg-blue-100 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-1000 ease-out"
                        style={{ width: `${currentResult?.stroma || 0}%` }}
                      />
                    </div>
                  </div>

                  {/* Tumor Content */}
                  <div className="text-center">
                    <div className="bg-red-50 rounded-full w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-red-600">
                          {currentResult?.tumor || 0}%
                        </div>
                      </div>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-800 mb-2">Tumor Content</h4>
                    <p className="text-sm text-gray-500">Malignant tissue percentage</p>
                    <div className="mt-3 bg-red-100 rounded-full h-2">
                      <div
                        className="bg-red-500 h-2 rounded-full transition-all duration-1000 ease-out"
                        style={{ width: `${currentResult?.tumor || 0}%` }}
                      />
                    </div>
                  </div>

                  {/* Blood Vessel Content */}
                  <div className="text-center">
                    <div className="bg-green-50 rounded-full w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-green-600">
                          {currentResult?.vessel || 0}%
                        </div>
                      </div>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-800 mb-2">Blood Vessel Content</h4>
                    <p className="text-sm text-gray-500">Vascular tissue percentage</p>
                    <div className="mt-3 bg-green-100 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full transition-all duration-1000 ease-out"
                        style={{ width: `${currentResult?.vessel || 0}%` }}
                      />
                    </div>
                  </div>
                </div>

                {/* Horizontal Bar Graph for Tissue Percentages */}
                <div className="mt-8">
                  <h4 className="text-lg font-semibold text-gray-800 mb-2 text-center">Tissue Percentage Distribution</h4>
                  <div className="flex rounded-full overflow-hidden h-8 bg-gray-200 shadow-inner">
                    <div
                      className="flex items-center justify-center bg-blue-500 text-white text-sm font-bold transition-all duration-1000 ease-out"
                      style={{ width: `${currentResult?.stroma || 0}%` }}
                    >
                      {currentResult?.stroma > 0 && (
                        <span className="px-2">Stroma {currentResult.stroma}%</span>
                      )}
                    </div>
                    <div
                      className="flex items-center justify-center bg-red-500 text-white text-sm font-bold transition-all duration-1000 ease-out"
                      style={{ width: `${currentResult?.tumor || 0}%` }}
                    >
                      {currentResult?.tumor > 0 && (
                        <span className="px-2">Tumor {currentResult.tumor}%</span>
                      )}
                    </div>
                    <div
                      className="flex items-center justify-center bg-green-500 text-white text-sm font-bold transition-all duration-1000 ease-out"
                      style={{ width: `${currentResult?.vessel || 0}%` }}
                    >
                      {currentResult?.vessel > 0 && (
                        <span className="px-2">Vessel {currentResult.vessel}%</span>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Clinical Ratios and Prognosis Section */}
              {clinicalMatrices && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="mt-8"
                >
                  <Card className="border-medical-background">
                    <CardHeader>
                      <CardTitle className="text-xl font-semibold text-primary-navy">
                        Clinical Analysis & Prognosis
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid md:grid-cols-2 gap-6">
                        {/* Clinical Ratios */}
                        <div className="space-y-4">
                          <h3 className="text-lg font-medium text-primary-navy mb-4">Clinical Ratios</h3>
                          <div className="grid gap-4">
                            <div className="p-4 bg-sky-50 rounded-lg">
                              <p className="text-sm text-gray-600 mb-1">Tumor-Stroma Ratio (TSR)</p>
                              <div className="flex items-center justify-between">
                                <p className="text-2xl font-bold text-cerulean">
                                  {(clinicalMatrices.tsr * 100).toFixed(1)}%
                                </p>
                                <Badge className={clinicalMatrices.tsrStatus === 'Good' ? 'bg-medical-vessel/20 text-medical-vessel' : 'bg-medical-tumor/20 text-medical-tumor'}>
                                  {clinicalMatrices.tsrStatus}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-500 mt-2">{getTSRInterpretation(clinicalMatrices.tsr)}</p>
                            </div>

                            <div className="p-4 bg-sky-50 rounded-lg">
                              <p className="text-sm text-gray-600 mb-1">Tumor-Vessel Ratio (TVR)</p>
                              <div className="flex items-center justify-between">
                                <p className="text-2xl font-bold text-cerulean">
                                  {clinicalMatrices.tvr.toFixed(2)}
                                </p>
                                <Badge className={clinicalMatrices.tvrStatus === 'Good' ? 'bg-medical-vessel/20 text-medical-vessel' : 'bg-medical-tumor/20 text-medical-tumor'}>
                                  {clinicalMatrices.tvrStatus}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-500 mt-2">{getTVRInterpretation(clinicalMatrices.tvr)}</p>
                            </div>

                            <div className="p-4 bg-sky-50 rounded-lg">
                              <p className="text-sm text-gray-600 mb-1">Stroma-Vessel Ratio (SVR)</p>
                              <div className="flex items-center justify-between">
                                <p className="text-2xl font-bold text-cerulean">
                                  {clinicalMatrices.svr.toFixed(2)}
                                </p>
                                <Badge className={clinicalMatrices.svrStatus === 'Good' ? 'bg-medical-vessel/20 text-medical-vessel' : 'bg-medical-tumor/20 text-medical-tumor'}>
                                  {clinicalMatrices.svrStatus}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-500 mt-2">{getSVRInterpretation(clinicalMatrices.svr)}</p>
                            </div>
                          </div>
                        </div>

                        {/* Overall Prognosis */}
                        <div className="p-6 bg-gradient-to-br from-medical-background to-white rounded-lg border border-medical-background/20">
                          <h3 className="text-lg font-medium text-primary-navy mb-4">Overall Prognosis</h3>
                          <div className="space-y-4">
                            <div className={`p-4 rounded-lg ${
                              clinicalMatrices.overallPrognosis === 'Good' 
                                ? 'bg-medical-vessel/10' 
                                : 'bg-medical-tumor/10'
                            }`}>
                              <div className="flex items-center justify-between mb-2">
                                <Badge 
                                  className={
                                    clinicalMatrices.overallPrognosis === 'Good'
                                      ? 'bg-medical-vessel text-white'
                                      : 'bg-medical-tumor text-white'
                                  }
                                >
                                  {clinicalMatrices.overallPrognosis} Prognosis
                                </Badge>
                                <span className={`text-sm ${
                                  clinicalMatrices.overallPrognosis === 'Good'
                                    ? 'text-medical-vessel'
                                    : 'text-medical-tumor'
                                }`}>
                                  {clinicalMatrices.confidenceScore.toFixed(1)}% Confidence
                                </span>
                              </div>

                              <div className="space-y-2">
                                {clinicalMatrices.prognosticFactors.map((factor, index) => (
                                  <div key={index} className="flex items-start gap-2">
                                    <span className={`w-2 h-2 rounded-full mt-1.5 ${
                                      factor.impact === 'positive' 
                                        ? 'bg-medical-vessel' 
                                        : 'bg-medical-tumor'
                                    }`} />
                                    <p className="text-sm text-gray-600">{factor.description}</p>
                                  </div>
                                ))}
                              </div>
                            </div>

                            <div className="text-sm text-gray-500">
                              <p className="mb-2 font-medium">Recommendations:</p>
                              <ul className="list-disc pl-4 space-y-1">
                                {clinicalMatrices.recommendations.map((rec, index) => (
                                  <li key={index}>{rec}</li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}

              {/* Existing Chart Section */}
              <Card className="border-sky-100 mt-8">
                <CardHeader>
                  <CardTitle>Detailed Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="chart" className="w-full">
                    <TabsList className="grid w-full grid-cols-4 max-w-2xl mx-auto">
                      <TabsTrigger value="chart" className="flex items-center gap-2">
                        <BarChart3Icon className="w-4 h-4" />
                        <span>Bar Chart</span>
                      </TabsTrigger>
                      <TabsTrigger value="pie" className="flex items-center gap-2">
                        <PieChartIcon className="w-4 h-4" />
                        <span>Distribution</span>
                      </TabsTrigger>
                      <TabsTrigger value="individual" className="flex items-center gap-2">
                        <BarChart3Icon className="w-4 h-4" />
                        <span>Individual Charts</span>
                      </TabsTrigger>
                      <TabsTrigger value="details" className="flex items-center gap-2">
                        <TableIcon className="w-4 h-4" />
                        <span>Details</span>
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="chart" className="mt-4">
                      <div className="h-[400px] w-full">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart data={barData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis />
                            <RechartsTooltip />
                            <Bar dataKey="stroma" fill="#4DA6FF" name="Stroma %" />
                            <Bar dataKey="tumor" fill="#FF5252" name="Tumor %" />
                            <Bar dataKey="vessel" fill="#4CAF50" name="Blood Vessel %" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </TabsContent>

                    <TabsContent value="pie" className="mt-4">
                      <div className="h-[400px] w-full">
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={pieData}
                              dataKey="value"
                              nameKey="name"
                              cx="50%"
                              cy="50%"
                              outerRadius={150}
                              label={({ name, value }) => `${name}: ${value}%`}
                            >
                              {pieData.map((entry) => (
                                <Cell key={entry.name} fill={entry.color} />
                              ))}
                            </Pie>
                            <RechartsTooltip />
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    </TabsContent>

                    <TabsContent value="individual" className="mt-4">
                      <div className="space-y-8">
                        {/* Individual Charts Header */}
                        <div className="text-center mb-6">
                          <h3 className="text-xl font-semibold text-gray-800 mb-2">Individual Tissue Type Analysis</h3>
                          <p className="text-gray-600">Separate charts showing distribution of each tissue type across all patches</p>
                        </div>

                        {/* Tumor Chart */}
                        <div className="bg-red-50 rounded-xl p-6 border border-red-100">
                          <h4 className="text-lg font-semibold text-red-700 mb-4 flex items-center gap-2">
                            <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                            Tumor Percentage Distribution
                          </h4>
                          <div className="h-[300px] w-full">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart data={barData}>
                                <CartesianGrid strokeDasharray="3 3" stroke="#fecaca" />
                                <XAxis dataKey="name" tick={{ fill: '#dc2626', fontSize: 12 }} />
                                <YAxis tick={{ fill: '#dc2626', fontSize: 12 }} />
                                <RechartsTooltip
                                  contentStyle={{
                                    backgroundColor: '#fef2f2',
                                    border: '1px solid #fecaca',
                                    borderRadius: '8px'
                                  }}
                                  formatter={(value) => [`${value}%`, 'Tumor']}
                                />
                                <Bar dataKey="tumor" fill="#ef4444" radius={[4, 4, 0, 0]} />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                          <div className="mt-4 text-center">
                            <div className="text-sm text-red-600">
                              Average: <span className="font-bold">{(results.reduce((sum, r) => sum + r.tumor, 0) / results.length).toFixed(1)}%</span>
                              {' | '}
                              Range: <span className="font-bold">{Math.min(...results.map(r => r.tumor))}% - {Math.max(...results.map(r => r.tumor))}%</span>
                            </div>
                          </div>
                        </div>

                        {/* Stroma Chart */}
                        <div className="bg-blue-50 rounded-xl p-6 border border-blue-100">
                          <h4 className="text-lg font-semibold text-blue-700 mb-4 flex items-center gap-2">
                            <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                            Stroma Percentage Distribution
                          </h4>
                          <div className="h-[300px] w-full">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart data={barData}>
                                <CartesianGrid strokeDasharray="3 3" stroke="#bfdbfe" />
                                <XAxis dataKey="name" tick={{ fill: '#2563eb', fontSize: 12 }} />
                                <YAxis tick={{ fill: '#2563eb', fontSize: 12 }} />
                                <RechartsTooltip
                                  contentStyle={{
                                    backgroundColor: '#eff6ff',
                                    border: '1px solid #bfdbfe',
                                    borderRadius: '8px'
                                  }}
                                  formatter={(value) => [`${value}%`, 'Stroma']}
                                />
                                <Bar dataKey="stroma" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                          <div className="mt-4 text-center">
                            <div className="text-sm text-blue-600">
                              Average: <span className="font-bold">{(results.reduce((sum, r) => sum + r.stroma, 0) / results.length).toFixed(1)}%</span>
                              {' | '}
                              Range: <span className="font-bold">{Math.min(...results.map(r => r.stroma))}% - {Math.max(...results.map(r => r.stroma))}%</span>
                            </div>
                          </div>
                        </div>

                        {/* Blood Vessel Chart */}
                        <div className="bg-green-50 rounded-xl p-6 border border-green-100">
                          <h4 className="text-lg font-semibold text-green-700 mb-4 flex items-center gap-2">
                            <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                            Blood Vessel Percentage Distribution
                          </h4>
                          <div className="h-[300px] w-full">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart data={barData}>
                                <CartesianGrid strokeDasharray="3 3" stroke="#bbf7d0" />
                                <XAxis dataKey="name" tick={{ fill: '#16a34a', fontSize: 12 }} />
                                <YAxis tick={{ fill: '#16a34a', fontSize: 12 }} />
                                <RechartsTooltip
                                  contentStyle={{
                                    backgroundColor: '#f0fdf4',
                                    border: '1px solid #bbf7d0',
                                    borderRadius: '8px'
                                  }}
                                  formatter={(value) => [`${value}%`, 'Blood Vessel']}
                                />
                                <Bar dataKey="vessel" fill="#22c55e" radius={[4, 4, 0, 0]} />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                          <div className="mt-4 text-center">
                            <div className="text-sm text-green-600">
                              Average: <span className="font-bold">{(results.reduce((sum, r) => sum + r.vessel, 0) / results.length).toFixed(1)}%</span>
                              {' | '}
                              Range: <span className="font-bold">{Math.min(...results.map(r => r.vessel))}% - {Math.max(...results.map(r => r.vessel))}%</span>
                            </div>
                          </div>
                        </div>

                        {/* Summary Statistics */}
                        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100">
                          <h4 className="text-lg font-semibold text-purple-700 mb-4 flex items-center gap-2">
                            <div className="w-4 h-4 bg-purple-500 rounded-full"></div>
                            Summary Statistics
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="text-center bg-white rounded-lg p-4 shadow-sm">
                              <div className="text-2xl font-bold text-red-600 mb-1">
                                {(results.reduce((sum, r) => sum + r.tumor, 0) / results.length).toFixed(1)}%
                              </div>
                              <div className="text-sm text-gray-600 mb-2">Average Tumor</div>
                              <div className="text-xs text-gray-500">
                                Std Dev: {Math.sqrt(results.reduce((sum, r) => sum + Math.pow(r.tumor - (results.reduce((s, res) => s + res.tumor, 0) / results.length), 2), 0) / results.length).toFixed(1)}%
                              </div>
                            </div>
                            <div className="text-center bg-white rounded-lg p-4 shadow-sm">
                              <div className="text-2xl font-bold text-blue-600 mb-1">
                                {(results.reduce((sum, r) => sum + r.stroma, 0) / results.length).toFixed(1)}%
                              </div>
                              <div className="text-sm text-gray-600 mb-2">Average Stroma</div>
                              <div className="text-xs text-gray-500">
                                Std Dev: {Math.sqrt(results.reduce((sum, r) => sum + Math.pow(r.stroma - (results.reduce((s, res) => s + res.stroma, 0) / results.length), 2), 0) / results.length).toFixed(1)}%
                              </div>
                            </div>
                            <div className="text-center bg-white rounded-lg p-4 shadow-sm">
                              <div className="text-2xl font-bold text-green-600 mb-1">
                                {(results.reduce((sum, r) => sum + r.vessel, 0) / results.length).toFixed(1)}%
                              </div>
                              <div className="text-sm text-gray-600 mb-2">Average Blood Vessel</div>
                              <div className="text-xs text-gray-500">
                                Std Dev: {Math.sqrt(results.reduce((sum, r) => sum + Math.pow(r.vessel - (results.reduce((s, res) => s + res.vessel, 0) / results.length), 2), 0) / results.length).toFixed(1)}%
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="details" className="mt-4">
                      <div className="space-y-6">
                        {results.map((result, index) => (
                          <motion.div
                            key={result.patchId || index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="bg-gray-50 rounded-lg p-6"
                          >
                            <h4 className="text-lg font-semibold text-gray-900 mb-4">
                              Patch {index + 1}: {result.patchId}
                            </h4>
                            <div className="grid grid-cols-3 gap-4">
                              <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">{result.stroma}%</div>
                                <div className="text-sm text-gray-600">Stroma</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-red-600">{result.tumor}%</div>
                                <div className="text-sm text-gray-600">Tumor</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-green-600">{result.vessel}%</div>
                                <div className="text-sm text-gray-600">Blood Vessel</div>
                              </div>
                            </div>
                            <div className="mt-4">
                              <div className="flex rounded-full overflow-hidden h-3 bg-gray-200">
                                <div
                                  className="bg-blue-500"
                                  style={{ width: `${result.stroma}%` }}
                                />
                                <div
                                  className="bg-red-500"
                                  style={{ width: `${result.tumor}%` }}
                                />
                                <div
                                  className="bg-green-500"
                                  style={{ width: `${result.vessel}%` }}
                                />
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>

              {/* Extreme Values Section */}
              <div className="mt-8">
                <h2 className="text-2xl font-bold mb-4">Tissue Distribution Extremes</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    {renderExtremeValueCard('tumor', 'highest')}
                    {renderExtremeValueCard('tumor', 'lowest')}
                  </div>
                  <div>
                    {renderExtremeValueCard('stroma', 'highest')}
                    {renderExtremeValueCard('stroma', 'lowest')}
                  </div>
                  <div>
                    {renderExtremeValueCard('vessel', 'highest')}
                    {renderExtremeValueCard('vessel', 'lowest')}
                  </div>
                </div>
              </div>
            </div>
          )}

          {!analyzing && !analysisComplete && files.length > 0 && (
            <div className="flex items-center justify-center h-40 border-2 border-dashed rounded-lg">
              <div className="text-center">
                <p className="text-sm text-gray-500 mb-2">Ready to analyze {files.length} image patches</p>
                <p className="text-xs text-gray-400">Click "Analyze Patches" to begin tissue segmentation</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AnalysisVisualizer;
